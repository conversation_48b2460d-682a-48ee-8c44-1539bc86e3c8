import { useState, useCallback, useEffect } from 'react';
import { Stock } from '@/types';
import { validateStockCode, formatStockCode, isDuplicateCode } from '@/utils/validation';
import { useCloudStorage } from './useCloudStorage';
import { StockInfo } from '@/types/stock';

// 常见股票名称映射
const STOCK_NAME_MAP: Record<string, string> = {
  '000001': '平安银行',
  '000002': '万科A',
  '000858': '五粮液',
  '600036': '招商银行',
  '600121': '郑州煤电',
  '600519': '贵州茅台',
  '002415': '海康威视',
  '600276': '恒瑞医药',
  '000725': '京东方A',
  '600793': '宜宾纸业',
  '603067': '振华股份',
  // 可以继续添加更多股票
};

/**
 * 获取股票名称
 * @param code 股票代码
 * @returns 股票名称
 */
function getStockName(code: string): string {
  return STOCK_NAME_MAP[code] || `股票${code}`;
}

interface UseStockListReturn {
  stocks: Stock[];
  addStock: (code: string, name?: string) => Promise<{ success: boolean; message?: string }>;
  removeStock: (code: string) => void;
  clearAllStocks: () => void;
  reorderStocks: (activeId: string, overId: string) => void;
  isLoading: boolean;
  error: string | null;
  // 云端存储相关
  syncStatus: any;
  userIdentity: any;
  lastModified: string;
  forceSyncToCloud: () => Promise<boolean>;
  forceLoadFromCloud: () => Promise<boolean>;
  createBackup: () => Promise<any>;
  restoreFromBackup: (backup: any) => Promise<boolean>;
  deleteCloudData: () => Promise<boolean>;
  setCustomUserId: (userId: string) => Promise<boolean>;
  getUserStats: () => Promise<any>;
}

const STORAGE_KEY = 'gupiao-stock-list';

/**
 * 股票列表管理Hook
 */
export function useStockList(): UseStockListReturn {
  const [stocks, setStocks] = useState<Stock[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastModified, setLastModified] = useState<string>(new Date().toISOString());

  // 云端存储管理
  const cloudStorage = useCloudStorage();

  // 初始化数据加载
  useEffect(() => {
    const initializeData = async () => {
      setIsLoading(true);
      try {
        // 首先从 localStorage 加载数据
        const savedStocks = localStorage.getItem(STORAGE_KEY);
        const savedLastModified = localStorage.getItem(`${STORAGE_KEY}_lastModified`);

        let localStocks: Stock[] = [];
        let localLastModified = new Date().toISOString();

        if (savedStocks) {
          localStocks = JSON.parse(savedStocks);
          // 确保股票有 sortOrder，如果没有则按索引分配
          localStocks = localStocks.map((stock, index) => ({
            ...stock,
            sortOrder: stock.sortOrder ?? index
          }));
          // 按 sortOrder 排序
          localStocks.sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0));
          localLastModified = savedLastModified || localLastModified;
        }

        // 设置本地数据
        setStocks(localStocks);
        setLastModified(localLastModified);

        // 如果在线，尝试从云端同步数据
        if (cloudStorage.syncStatus.isOnline && cloudStorage.userIdentity) {
          try {
            const cloudData = await cloudStorage.getFromCloud();

            if (cloudData && cloudData.stocks.length > 0) {
              // 比较时间戳，决定使用哪个数据
              const cloudTime = new Date(cloudData.lastModified).getTime();
              const localTime = new Date(localLastModified).getTime();

              if (cloudTime > localTime) {
                // 云端数据更新，使用云端数据
                const convertedStocks = cloudData.stocks.map((stock, index) => ({
                  code: stock.code,
                  name: stock.name,
                  sortOrder: stock.sortOrder ?? index,
                }));
                // 按 sortOrder 排序
                convertedStocks.sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0));

                setStocks(convertedStocks);
                setLastModified(cloudData.lastModified);
                saveToStorage(convertedStocks, cloudData.lastModified);
              } else if (localTime > cloudTime && localStocks.length > 0) {
                // 本地数据更新，同步到云端
                const stockInfos: StockInfo[] = localStocks.map(stock => ({
                  code: stock.code,
                  name: stock.name,
                  addedAt: new Date().toISOString(),
                  sortOrder: stock.sortOrder,
                }));

                await cloudStorage.syncToCloud(stockInfos, localLastModified);
              }
            } else if (localStocks.length > 0) {
              // 云端无数据，上传本地数据
              const stockInfos: StockInfo[] = localStocks.map(stock => ({
                code: stock.code,
                name: stock.name,
                addedAt: new Date().toISOString(),
                sortOrder: stock.sortOrder,
              }));

              await cloudStorage.syncToCloud(stockInfos, localLastModified);
            }
          } catch (cloudError) {
            console.warn('云端数据同步失败，使用本地数据:', cloudError);
          }
        }
      } catch (err) {
        console.error('初始化数据失败:', err);
        setError('初始化数据失败');
      } finally {
        setIsLoading(false);
      }
    };

    if (cloudStorage.userIdentity) {
      initializeData();
    }
  }, [cloudStorage.userIdentity, cloudStorage.syncStatus.isOnline]);

  // 保存股票列表到localStorage和云端
  const saveToStorage = useCallback((stockList: Stock[], timestamp?: string) => {
    const saveTime = timestamp || new Date().toISOString();

    try {
      // 保存到 localStorage
      localStorage.setItem(STORAGE_KEY, JSON.stringify(stockList));
      localStorage.setItem(`${STORAGE_KEY}_lastModified`, saveTime);

      setLastModified(saveTime);

      // 标记本地数据有变更，触发云端同步
      if (cloudStorage.syncStatus.isOnline) {
        cloudStorage.markLocalChanges();

        // 异步同步到云端
        const stockInfos: StockInfo[] = stockList.map(stock => ({
          code: stock.code,
          name: stock.name,
          addedAt: new Date().toISOString(),
          sortOrder: stock.sortOrder,
        }));

        cloudStorage.syncToCloud(stockInfos, saveTime).catch(error => {
          console.warn('云端同步失败:', error);
        });
      }
    } catch (err) {
      console.error('保存股票列表失败:', err);
      setError('保存股票列表失败');
    }
  }, [cloudStorage]);

  // 添加股票
  const addStock = useCallback(async (code: string, name?: string): Promise<{ success: boolean; message?: string }> => {
    setIsLoading(true);
    setError(null);

    try {
      // 验证股票代码
      const validation = validateStockCode(code);
      if (!validation.isValid) {
        return { success: false, message: validation.message };
      }

      const formattedCode = formatStockCode(code);
      const existingCodes = stocks.map(stock => stock.code);

      // 检查重复
      if (isDuplicateCode(formattedCode, existingCodes)) {
        return { success: false, message: '股票代码已存在' };
      }

      // 使用传入的名称，如果没有则使用映射表或默认格式
      const stockName = name || getStockName(formattedCode);

      // 创建新股票对象，分配最大的 sortOrder + 1
      const maxSortOrder = stocks.length > 0 ? Math.max(...stocks.map(s => s.sortOrder || 0)) : -1;
      const newStock: Stock = {
        code: formattedCode,
        name: stockName,
        sortOrder: maxSortOrder + 1,
      };

      // 更新股票列表
      const updatedStocks = [...stocks, newStock];
      setStocks(updatedStocks);
      saveToStorage(updatedStocks);

      return { success: true, message: '股票添加成功' };
    } catch (err) {
      const errorMessage = '添加股票失败';
      setError(errorMessage);
      return { success: false, message: errorMessage };
    } finally {
      setIsLoading(false);
    }
  }, [stocks, saveToStorage]);

  // 删除股票
  const removeStock = useCallback((code: string) => {
    setError(null);
    
    try {
      const updatedStocks = stocks.filter(stock => stock.code !== code);
      setStocks(updatedStocks);
      saveToStorage(updatedStocks);
    } catch (err) {
      console.error('删除股票失败:', err);
      setError('删除股票失败');
    }
  }, [stocks, saveToStorage]);

  // 清空所有股票
  const clearAllStocks = useCallback(() => {
    setError(null);

    try {
      setStocks([]);
      saveToStorage([]);
    } catch (err) {
      console.error('清空股票列表失败:', err);
      setError('清空股票列表失败');
    }
  }, [saveToStorage]);

  // 重新排序股票
  const reorderStocks = useCallback((activeId: string, overId: string) => {
    setError(null);

    try {
      const activeIndex = stocks.findIndex(stock => stock.code === activeId);
      const overIndex = stocks.findIndex(stock => stock.code === overId);

      if (activeIndex === -1 || overIndex === -1) {
        return;
      }

      // 创建新的股票数组
      const newStocks = [...stocks];
      const [movedStock] = newStocks.splice(activeIndex, 1);
      newStocks.splice(overIndex, 0, movedStock);

      // 重新分配 sortOrder
      const updatedStocks = newStocks.map((stock, index) => ({
        ...stock,
        sortOrder: index,
      }));

      setStocks(updatedStocks);
      saveToStorage(updatedStocks);
    } catch (err) {
      console.error('重新排序失败:', err);
      setError('重新排序失败');
    }
  }, [stocks, saveToStorage]);

  // 强制同步到云端
  const forceSyncToCloud = useCallback(async (): Promise<boolean> => {
    if (!cloudStorage.syncStatus.isOnline || !cloudStorage.userIdentity) {
      return false;
    }

    setIsLoading(true);
    try {
      const stockInfos: StockInfo[] = stocks.map(stock => ({
        code: stock.code,
        name: stock.name,
        addedAt: new Date().toISOString(),
        sortOrder: stock.sortOrder,
      }));

      const result = await cloudStorage.syncToCloud(stockInfos, lastModified, true);
      return result !== null;
    } catch (error) {
      console.error('强制同步失败:', error);
      setError('强制同步失败');
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [stocks, lastModified, cloudStorage]);

  // 从云端强制拉取数据
  const forceLoadFromCloud = useCallback(async (): Promise<boolean> => {
    if (!cloudStorage.syncStatus.isOnline || !cloudStorage.userIdentity) {
      return false;
    }

    setIsLoading(true);
    try {
      const cloudData = await cloudStorage.getFromCloud();

      if (cloudData) {
        const convertedStocks = cloudData.stocks.map((stock, index) => ({
          code: stock.code,
          name: stock.name,
          sortOrder: stock.sortOrder ?? index,
        }));
        // 按 sortOrder 排序
        convertedStocks.sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0));

        setStocks(convertedStocks);
        setLastModified(cloudData.lastModified);
        saveToStorage(convertedStocks, cloudData.lastModified);
        return true;
      }

      return false;
    } catch (error) {
      console.error('从云端加载失败:', error);
      setError('从云端加载失败');
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [cloudStorage, saveToStorage]);

  return {
    stocks,
    addStock,
    removeStock,
    clearAllStocks,
    reorderStocks,
    isLoading,
    error,
    // 云端存储相关
    syncStatus: cloudStorage.syncStatus,
    userIdentity: cloudStorage.userIdentity,
    lastModified,
    forceSyncToCloud,
    forceLoadFromCloud,
    createBackup: cloudStorage.createBackup,
    restoreFromBackup: cloudStorage.restoreFromBackup,
    deleteCloudData: cloudStorage.deleteCloudData,
    setCustomUserId: cloudStorage.setCustomUserId,
    getUserStats: cloudStorage.getUserStats,
  };
}
